{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "guzzlehttp/guzzle": "^7.0.1", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.21", "laravel/tinker": "^2.9"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.28", "mockery/mockery": "^1.6.7", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^10.5", "spatie/laravel-ignition": "^2.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}