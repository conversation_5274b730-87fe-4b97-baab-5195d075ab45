<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Update token columns to TEXT to store JWT tokens
            $table->text('bexio_access_token')->nullable()->change();
            $table->text('bexio_refresh_token')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Revert back to VARCHAR(255)
            $table->string('bexio_access_token')->nullable()->change();
            $table->string('bexio_refresh_token')->nullable()->change();
        });
    }
};
