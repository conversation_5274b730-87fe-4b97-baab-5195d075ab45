<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add subscription_status to organizations table if not exists
        Schema::table('organizations', function (Blueprint $table) {
            if (!Schema::hasColumn('organizations', 'subscription_status')) {
                $table->enum('subscription_status', ['trial', 'active', 'inactive'])
                      ->default('trial')
                      ->after('status');
            }
        });

        // Update users table token columns to TEXT for JWT storage
        Schema::table('users', function (Blueprint $table) {
            $table->text('bexio_access_token')->nullable()->change();
            $table->text('bexio_refresh_token')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->dropColumn('subscription_status');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->string('bexio_access_token')->nullable()->change();
            $table->string('bexio_refresh_token')->nullable()->change();
        });
    }
};
