<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update organizations table to match PHP original
        Schema::table('organizations', function (Blueprint $table) {
            $table->renameColumn('bexio_organization_id', 'bexio_org_id');
            $table->date('subscription_start')->nullable();
            $table->enum('subscription_model', ['monthly', 'yearly'])->nullable();
        });

        // Update users table to match PHP original
        Schema::table('users', function (Blueprint $table) {
            $table->renameColumn('bexio_access_token', 'access_token');
            $table->renameColumn('bexio_refresh_token', 'refresh_token');
            $table->renameColumn('bexio_token_expires_at', 'token_expires_at');
            $table->datetime('refresh_token_rotated_at')->default(now());
            $table->boolean('is_admin')->default(false);
            $table->dropColumn(['role', 'is_active', 'last_login_at', 'bexio_user_profile']);
        });

        // Update recurring_templates table to match PHP original
        Schema::table('recurring_templates', function (Blueprint $table) {
            $table->integer('contact_id')->nullable();
            $table->string('title')->nullable();
            $table->json('positions')->nullable();
            $table->date('start_date')->nullable();
            $table->string('interval_str', 20)->nullable();
            $table->date('last_executed')->nullable();
            $table->dropColumn(['invoice_id', 'interval', 'next_run']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the changes
        Schema::table('organizations', function (Blueprint $table) {
            $table->renameColumn('bexio_org_id', 'bexio_organization_id');
            $table->dropColumn(['subscription_start', 'subscription_model']);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->renameColumn('access_token', 'bexio_access_token');
            $table->renameColumn('refresh_token', 'bexio_refresh_token');
            $table->renameColumn('token_expires_at', 'bexio_token_expires_at');
            $table->dropColumn(['refresh_token_rotated_at', 'is_admin']);
            $table->enum('role', ['user', 'admin', 'super_admin'])->default('user');
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_login_at')->nullable();
            $table->json('bexio_user_profile')->nullable();
        });

        Schema::table('recurring_templates', function (Blueprint $table) {
            $table->dropColumn(['contact_id', 'title', 'positions', 'start_date', 'interval_str', 'last_executed']);
            $table->foreignId('invoice_id')->nullable()->constrained();
            $table->enum('interval', ['daily', 'weekly', 'monthly']);
            $table->timestamp('next_run')->nullable();
        });
    }
};
