@extends('layouts.app')

@section('title', 'Dashboard')

@section('content')
<div class="container-fluid">
    @if (session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="mb-2">
                <h6 class="text-muted mb-1">Welcome back, {{ Auth::user()->name }}</h6>
                <h1 class="display-5 fw-bold mb-0">Dashboard</h1>
            </div>
        </div>
        <div class="col-md-6">
            <div class="row g-3">
                <div class="col-6">
                    <div class="card bg-light border-0">
                        <div class="card-body p-3">
                            <h6 class="text-muted small mb-1">Total Invoices</h6>
                            <h4 class="fw-bold mb-0">{{ count($invoices) }}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="card bg-light border-0">
                        <div class="card-body p-3">
                            <h6 class="text-muted small mb-1">Active Invoices</h6>
                            <h4 class="fw-bold mb-0">{{ count(array_filter($invoices, fn($inv) => $inv['is_valid'])) }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row g-4">
        <!-- Invoices Section -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-semibold">Recent Invoices</h5>
                    <button class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Create New
                    </button>
                </div>
                <div class="card-body p-0">
                    @if(count($invoices) > 0)
                        @foreach(array_slice($invoices, 0, 5) as $index => $invoice)
                            <div class="d-flex align-items-center p-3 {{ $index > 0 ? 'border-top' : '' }}">
                                <div class="me-3">
                                    <div class="bg-dark text-white rounded d-flex align-items-center justify-content-center" style="width: 45px; height: 45px;">
                                        <i class="fas fa-file-invoice"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $invoice['title'] ?? $invoice['document_nr'] }}</h6>
                                    <small class="text-muted">{{ $invoice['document_nr'] }}</small>
                                    <div class="mt-1">
                                        <span class="badge {{ $invoice['is_valid'] ? 'bg-success' : 'bg-secondary' }} bg-opacity-10 text-{{ $invoice['is_valid'] ? 'success' : 'secondary' }}">
                                            {{ $invoice['is_valid'] ? 'Active' : 'Inactive' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <h6 class="mb-0">${{ number_format($invoice['total'], 2) }}</h6>
                                </div>
                            </div>
                        @endforeach
                        @if(count($invoices) > 5)
                            <div class="text-center p-3 border-top">
                                <a href="#" class="btn btn-outline-secondary btn-sm">View All Invoices</a>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-invoice fa-2x text-muted mb-3"></i>
                            <h6 class="text-muted">You don't have any invoices</h6>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 fw-semibold">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <button class="btn btn-outline-primary d-flex align-items-center">
                            <i class="fas fa-plus me-2"></i>
                            Create New Invoice
                        </button>
                        <button class="btn btn-outline-secondary d-flex align-items-center">
                            <i class="fas fa-file-alt me-2"></i>
                            View Drafts
                        </button>
                        <button class="btn btn-outline-info d-flex align-items-center">
                            <i class="fas fa-cog me-2"></i>
                            Settings
                        </button>
                        <hr>
                        <form method="POST" action="{{ route('logout') }}" class="mb-0">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger w-100 d-flex align-items-center justify-content-center">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
