<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Auth\BexioAuthController;
use App\Services\BexioServiceInterface;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return Auth::check() ? redirect('/dashboard') : redirect('/login');
});

Route::get('/login', [BexioAuthController::class, 'showLoginForm'])
    ->name('login')
    ->middleware('guest');

Route::get('/auth/bexio', [BexioAuthController::class, 'redirect'])
    ->name('bexio.login')
    ->middleware('guest');

Route::get('/auth/bexio/callback', [BexioAuthController::class, 'callback'])
    ->name('bexio.auth.callback');

Route::post('/logout', [BexioAuthController::class, 'logout'])
    ->name('logout')
    ->middleware('auth');

Route::get('/home', function () {
    return redirect('/dashboard');
});

Route::get('/dashboard', function () {
    $user = Auth::user();
    $bexioService = app(BexioServiceInterface::class);

    // Set access token from user if available
    if ($user && $user->bexio_access_token && method_exists($bexioService, 'setAccessToken')) {
        $bexioService->setAccessToken($user->bexio_access_token);
    }

    $invoices = $bexioService->getInvoices();
    $recurringInvoices = $bexioService->getRecurringInvoices();
    $drafts = $bexioService->getDrafts();

    return view('dashboard', [
        'invoices' => $invoices,
        'recurringInvoices' => $recurringInvoices,
        'drafts' => $drafts,
        'unpaidInvoices' => collect($invoices)->where('status', '!=', 'paid')->count(),
        'totalRevenue' => collect($invoices)->where('status', 'paid')->sum('total')
    ]);
})->middleware('auth');
