<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\BexioAuthController;
use App\Services\BexioServiceInterface;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return auth()->check() ? redirect('/dashboard') : redirect('/auth/bexio');
});

Route::get('/auth/bexio', [BexioAuthController::class, 'redirect'])
    ->name('bexio.login');

Route::get('/auth/bexio/callback', [BexioAuthController::class, 'callback'])
    ->name('bexio.auth.callback');

Route::post('/logout', [BexioAuthController::class, 'logout'])
    ->name('logout');

Route::get('/home', function () {
    return redirect('/dashboard');
});

Route::get('/dashboard', function () {
    return view('dashboard', [
        'invoices' => app(BexioServiceInterface::class)->getInvoices()
    ]);
})->middleware('auth');
