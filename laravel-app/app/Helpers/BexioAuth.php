<?php

namespace App\Helpers;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>jett\OpenIDConnectClient;

class BexioAuth
{
    /**
     * Require login and fresh token - matching PHP original requireLoginAndFreshToken
     */
    public static function requireLoginAndFreshToken()
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Check if token needs refresh (300 seconds = 5 minutes before expiry)
        if ($user->token_expires_at &&
            $user->token_expires_at->diffInSeconds(now()) < 300) {

            return self::refreshToken($user);
        }

        return null; // No redirect needed
    }

    /**
     * Refresh token using OpenIDConnectClient - matching PHP original
     */
    private static function refreshToken(User $user)
    {
        try {
            $clientId = config('bexio.client_id');
            $clientSecret = config('bexio.client_secret');

            if (!$clientId || !$clientSecret) {
                Log::error('Bexio client credentials not configured');
                throw new \Exception('Bexio client credentials missing');
            }

            $oidc = new OpenIDConnectClient(
                'https://auth.bexio.com/realms/bexio',
                $clientId,
                $clientSecret
            );

            $oidc->providerConfigParam([
                'token_endpoint' => 'https://auth.bexio.com/realms/bexio/protocol/openid-connect/token',
            ]);

            // Refresh the token
            $oidc->refreshToken($user->refresh_token);

            $newAccessToken = $oidc->getAccessToken();
            $newRefreshToken = $oidc->getRefreshToken();

            if ($newAccessToken) {
                $user->update([
                    'access_token' => $newAccessToken,
                    'refresh_token' => $newRefreshToken ?: $user->refresh_token,
                    'token_expires_at' => now()->addSeconds(3600), // 1 hour
                    'refresh_token_rotated_at' => now(),
                ]);

                Log::info("Token refreshed successfully for user {$user->id}");
                return null; // Success, no redirect needed
            }

        } catch (\Exception $e) {
            Log::error("Token refresh failed for user {$user->id}: " . $e->getMessage());
        }

        // If refresh failed, logout and redirect to login
        // But don't show "session expired" message if this is during OAuth flow
        Auth::logout();
        $message = request()->is('auth/*') ? 'Authentication failed' : 'Session expired, please login again';
        return redirect()->route('login')->withErrors(['auth' => $message]);
    }

    /**
     * Get current user's access token
     */
    public static function getAccessToken()
    {
        $user = Auth::user();
        return $user ? $user->access_token : null;
    }

    /**
     * Check if user has admin privileges
     */
    public static function isAdmin()
    {
        $user = Auth::user();
        return $user && $user->is_admin;
    }
}
