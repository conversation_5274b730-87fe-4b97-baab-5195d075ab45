<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RecurringTemplate extends Model
{
    protected $fillable = [
        'user_id',
        'invoice_id',
        'interval',
        'next_run'
    ];

    protected $casts = [
        'next_run' => 'date',
        'interval' => 'string'
    ];

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }
}
