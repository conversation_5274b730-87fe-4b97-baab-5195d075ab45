<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    protected $fillable = [
        'user_id',
        'bexio_id',
        'document_nr',
        'contact_info',
        'total',
        'status',
        'is_recurring',
        'recurring_settings'
    ];

    protected $casts = [
        'contact_info' => 'array',
        'recurring_settings' => 'array',
        'is_recurring' => 'boolean'
    ];

    public function recurringTemplate()
    {
        return $this->hasOne(RecurringTemplate::class);
    }
}
