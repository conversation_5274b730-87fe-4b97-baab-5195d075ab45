<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Organization extends Model
{
    use HasFactory;

    protected $fillable = [
        'bexio_organization_id',
        'name',
        'email',
        'country',
        'language',
        'status',
        'trial_ends_at',
        'activated_at',
        'bexio_company_profile',
    ];

    protected $casts = [
        'trial_ends_at' => 'datetime',
        'activated_at' => 'datetime',
        'bexio_company_profile' => 'array',
    ];

    // Relationships
    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function subscription()
    {
        return $this->hasOne(Subscription::class);
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function recurringTemplates()
    {
        return $this->hasMany(RecurringTemplate::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeTrial($query)
    {
        return $query->where('status', 'trial');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    // Helper Methods
    public function isActive()
    {
        return $this->status === 'active';
    }

    public function isTrial()
    {
        return $this->status === 'trial';
    }

    public function isInactive()
    {
        return $this->status === 'inactive';
    }

    public function isTrialExpired()
    {
        return $this->trial_ends_at && $this->trial_ends_at->isPast();
    }

    public function getDaysLeftInTrial()
    {
        if (!$this->trial_ends_at) {
            return null;
        }

        return max(0, now()->diffInDays($this->trial_ends_at, false));
    }

    public function canCreateInvoices()
    {
        return $this->isActive() || ($this->isTrial() && !$this->isTrialExpired());
    }

    public function activate()
    {
        $this->update([
            'status' => 'active',
            'activated_at' => now(),
        ]);
    }

    public function deactivate()
    {
        $this->update([
            'status' => 'inactive',
        ]);
    }
}
