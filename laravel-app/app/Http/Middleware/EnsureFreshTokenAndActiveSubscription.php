<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class EnsureFreshTokenAndActiveSubscription
{
    /**
     * Handle an incoming request - equivalent to requireLoginAndFreshToken()
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Check if user has organization
        if (!$user->organization) {
            Auth::logout();
            return redirect()->route('login')->withErrors(['auth' => 'No organization found']);
        }

        $organization = $user->organization;

        // Check subscription status - equivalent to subscription_status check
        if ($organization->status === 'inactive') {
            return response()->view('subscription.inactive', [
                'organization' => $organization,
                'user' => $user
            ], 403);
        }

        // Check if trial expired and no active subscription
        if ($organization->isTrial() && $organization->isTrialExpired()) {
            return response()->view('subscription.trial-expired', [
                'organization' => $organization,
                'user' => $user
            ], 403);
        }

        // Auto-refresh token if expiring within 5 minutes (300 seconds) - matching PHP original
        if ($user->token_expires_at &&
            $user->token_expires_at->diffInSeconds(now()) < 300) {

            $this->refreshBexioToken($user);
        }

        return $next($request);
    }

    private function refreshBexioToken($user)
    {
        try {
            $clientId = config('bexio.client_id');
            $clientSecret = config('bexio.client_secret');

            $response = Http::asForm()->post('https://auth.bexio.com/oauth/access_token', [
                'grant_type' => 'refresh_token',
                'refresh_token' => $user->refresh_token,
                'client_id' => $clientId,
                'client_secret' => $clientSecret,
            ]);

            if ($response->successful()) {
                $data = $response->json();

                $user->update([
                    'access_token' => $data['access_token'],
                    'refresh_token' => $data['refresh_token'] ?? $user->refresh_token,
                    'token_expires_at' => now()->addSeconds($data['expires_in']),
                ]);

                Log::info("Token refreshed successfully for user {$user->id}");
            } else {
                Log::error("Token refresh failed for user {$user->id}: " . $response->body());
                Auth::logout();
                return redirect()->route('login')->withErrors(['auth' => 'Token refresh failed']);
            }
        } catch (\Exception $e) {
            Log::error("Token refresh exception for user {$user->id}: " . $e->getMessage());
            Auth::logout();
            return redirect()->route('login')->withErrors(['auth' => 'Authentication failed']);
        }
    }
}
