<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Organization;
use App\Models\Subscription;
use App\Services\BexioServiceInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Ju<PERSON>jett\OpenIDConnectClient;

class BexioAuthController extends Controller
{
    public function __construct(
        private BexioServiceInterface $bexioService
    ) {}

    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function redirect()
    {
        // Use OpenIDConnectClient like PHP original
        try {
            $oidc = new OpenIDConnectClient(
                'https://auth.bexio.com/realms/bexio',
                config('bexio.client_id'),
                config('bexio.client_secret')
            );

            $oidc->setRedirectURL(url('/auth/callback'));
            $oidc->addScope(['openid', 'profile', 'email', 'accounting']);

            $oidc->authenticate();

        } catch (\Exception $e) {
            Log::error('Bexio OAuth redirect failed: ' . $e->getMessage());
            return redirect()->route('login')->withErrors(['auth' => 'Authentication failed']);
        }
    }

    public function callback()
    {
        try {
            // Use OpenIDConnectClient like PHP original
            $oidc = new OpenIDConnectClient(
                'https://auth.bexio.com/realms/bexio',
                config('bexio.client_id'),
                config('bexio.client_secret')
            );

            $oidc->setRedirectURL(url('/auth/callback'));
            $oidc->addScope(['openid', 'profile', 'email', 'accounting']);

            // Authenticate and get tokens
            $oidc->authenticate();

            $accessToken = $oidc->getAccessToken();
            $refreshToken = $oidc->getRefreshToken();
            $expiresIn = 3600; // bexio default (1h)

            if (!$accessToken) {
                return redirect('/login')->withErrors([
                    'bexio' => 'Failed to obtain access token'
                ]);
            }

            // Get user and company data from Bexio using the service
            $this->bexioService->setAccessToken($accessToken);
            $userData = $this->bexioService->getUserData();
            $companyData = $this->bexioService->getCompanyProfile();

            $tokenData = [
                'access_token' => $accessToken,
                'refresh_token' => $refreshToken,
                'expires_in' => $expiresIn,
            ];

            return DB::transaction(function () use ($tokenData, $userData, $companyData) {
                // Create or update organization
                $organization = Organization::updateOrCreate(
                    ['bexio_org_id' => $companyData['id'] ?? 'demo_org'],
                    [
                        'name' => $companyData['name'] ?? 'Demo Organization',
                        'email' => $companyData['email'] ?? '<EMAIL>',
                        'country' => $companyData['country'] ?? 'CH',
                        'language' => $companyData['language'] ?? 'de',
                        'bexio_company_profile' => $companyData,
                        'subscription_status' => 'trial',
                        'subscription_start' => now()->toDateString(),
                    ]
                );

                // Set trial period for new organizations
                if ($organization->wasRecentlyCreated) {
                    $organization->update([
                        'trial_ends_at' => now()->addMonths(3),
                        'status' => 'trial'
                    ]);
                }

                // Create or update user
                $user = User::updateOrCreate(
                    ['bexio_id' => $userData['id'] ?? 'demo_user'],
                    [
                        'name' => isset($userData['first_name']) && isset($userData['last_name'])
                            ? $userData['first_name'].' '.$userData['last_name']
                            : ($userData['name'] ?? 'Demo User'),
                        'email' => $userData['email'] ?? '<EMAIL>',
                        'organization_id' => $organization->id,
                        'is_admin' => $this->determineUserRole($userData, $organization) === 'admin',
                        'access_token' => $tokenData['access_token'],
                        'refresh_token' => $tokenData['refresh_token'] ?? null,
                        'token_expires_at' => isset($tokenData['expires_in'])
                            ? now()->addSeconds($tokenData['expires_in'])
                            : now()->addHour(),
                        'refresh_token_rotated_at' => now(),
                        'last_login_at' => now(),
                    ]
                );

                // Create subscription if it doesn't exist
                if (!$organization->subscription) {
                    Subscription::create([
                        'organization_id' => $organization->id,
                        'plan_type' => 'monthly',
                        'price' => 29.00, // Default monthly price
                        'status' => 'trial',
                        'trial_ends_at' => $organization->trial_ends_at,
                    ]);
                }

                Auth::login($user);

                // Send welcome email for new organizations
                if ($organization->wasRecentlyCreated) {
                    $this->sendWelcomeEmail($user, $organization);
                }

                return redirect('/dashboard');
            });

        } catch (\Exception $e) {
            Log::error('Bexio authentication failed: ' . $e->getMessage());
            return redirect('/login')->withErrors([
                'bexio' => 'Failed to authenticate with Bexio'
            ]);
        }
    }

    public function logout()
    {
        Auth::logout();
        return redirect('/');
    }

    private function determineUserRole($userData, $organization)
    {
        // First user in organization becomes admin
        if ($organization->users()->count() === 0) {
            return 'admin';
        }

        return 'user';
    }

    private function sendWelcomeEmail($user, $organization)
    {
        // TODO: Implement welcome email sending
        Log::info("Welcome email should be sent to {$user->email} for organization {$organization->name}");
    }

    public function personalTokenLogin()
    {
        try {
            // For personal token mode, simulate the callback with token data
            $tokenData = $this->bexioService->handleCallback('personal_token');

            if (!$tokenData || !isset($tokenData['access_token'])) {
                return redirect('/login')->withErrors([
                    'bexio' => 'Failed to setup personal token access'
                ]);
            }

            // Get user and company data from Bexio using personal token
            $this->bexioService->setAccessToken($tokenData['access_token']);
            $userData = $this->bexioService->getUserData();
            $companyData = $this->bexioService->getCompanyProfile();

            if (empty($userData) || empty($companyData)) {
                return redirect('/login')->withErrors([
                    'bexio' => 'Failed to retrieve data from Bexio. Please check your personal token.'
                ]);
            }

            return DB::transaction(function () use ($tokenData, $userData, $companyData) {
                // Create or update organization
                $organization = Organization::updateOrCreate(
                    ['bexio_organization_id' => $companyData['id'] ?? 'personal_token_org'],
                    [
                        'name' => $companyData['name'] ?? 'Personal Token Organization',
                        'email' => $companyData['email'] ?? '<EMAIL>',
                        'country' => $companyData['country'] ?? 'CH',
                        'language' => $companyData['language'] ?? 'de',
                        'bexio_company_profile' => $companyData,
                    ]
                );

                // Set trial period for new organizations
                if ($organization->wasRecentlyCreated) {
                    $organization->update([
                        'trial_ends_at' => now()->addMonths(3),
                        'status' => 'trial'
                    ]);
                }

                // Create or update user
                $user = User::updateOrCreate(
                    ['bexio_id' => $userData['id'] ?? 'personal_token_user'],
                    [
                        'name' => isset($userData['firstname']) && isset($userData['lastname'])
                            ? $userData['firstname'].' '.$userData['lastname']
                            : ($userData['name'] ?? 'Personal Token User'),
                        'email' => $userData['email'] ?? '<EMAIL>',
                        'organization_id' => $organization->id,
                        'role' => $this->determineUserRole($userData, $organization),
                        'bexio_access_token' => $tokenData['access_token'],
                        'bexio_refresh_token' => null, // Personal tokens don't have refresh tokens
                        'bexio_token_expires_at' => now()->addYear(), // Personal tokens don't expire
                        'bexio_user_profile' => $userData,
                        'last_login_at' => now(),
                    ]
                );

                // Create subscription if it doesn't exist
                if (!$organization->subscription) {
                    Subscription::create([
                        'organization_id' => $organization->id,
                        'plan_type' => 'monthly',
                        'price' => 29.00,
                        'status' => 'trial',
                        'trial_ends_at' => $organization->trial_ends_at,
                    ]);
                }

                Auth::login($user);

                // Send welcome email for new organizations
                if ($organization->wasRecentlyCreated) {
                    $this->sendWelcomeEmail($user, $organization);
                }

                return redirect('/dashboard');
            });

        } catch (\Exception $e) {
            Log::error('Personal token authentication failed: ' . $e->getMessage());
            return redirect('/login')->withErrors([
                'bexio' => 'Failed to authenticate with personal token: ' . $e->getMessage()
            ]);
        }
    }
}
