<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\BexioServiceInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class BexioAuthController extends Controller
{
    public function __construct(
        private BexioServiceInterface $bexioService
    ) {}

    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function redirect()
    {
        return redirect($this->bexioService->getAuthUrl());
    }

    public function callback()
    {
        try {
            $code = request()->query('code');
            $tokenData = $this->bexioService->handleCallback($code);
            $userData = $this->bexioService->getUserData();

            $user = User::updateOrCreate(
                ['bexio_id' => $userData['id']],
                [
                    'name' => $userData['first_name'].' '.$userData['last_name'],
                    'email' => $userData['email'],
                    'bexio_access_token' => $tokenData['access_token'],
                    'bexio_refresh_token' => $tokenData['refresh_token'] ?? null,
                    'bexio_token_expires_at' => now()->addSeconds($tokenData['expires_in']),
                ]
            );

            Auth::login($user);
            return redirect('/dashboard');
        } catch (\Exception $e) {
            Log::error('Bexio authentication failed: ' . $e->getMessage());
            return redirect('/login')->withErrors([
                'bexio' => 'Failed to authenticate with Bexio'
            ]);
        }
    }

    public function logout()
    {
        Auth::logout();
        return redirect('/');
    }
}
