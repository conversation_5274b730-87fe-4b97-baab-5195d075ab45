<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\User;

class BexioMockService implements BexioServiceInterface
{
    protected ?string $accessToken = null;
    protected array $mockUser = [
        'id' => 12345,
        'email' => '<EMAIL>',
        'first_name' => 'Demo',
        'last_name' => 'User',
        'company' => 'Bexio Demo'
    ];

    public function getAuthUrl(): string
    {
        return route('bexio.auth.callback') . '?code=mock_code';
    }

    public function handleCallback(string $code): array
    {
        if ($code === 'mock_code') {
            $this->accessToken = 'mock_access_token';
            return [
                'access_token' => $this->accessToken,
                'token_type' => 'Bearer',
                'expires_in' => 3600
            ];
        }

        return [];
    }

    public function getAccessToken(): ?string
    {
        return $this->accessToken;
    }

    public function setAccessToken(string $token): void
    {
        $this->accessToken = $token;
    }

    public function refreshToken(): array
    {
        $this->accessToken = 'refreshed_mock_token';
        return [
            'access_token' => $this->accessToken,
            'token_type' => 'Bearer',
            'expires_in' => 3600
        ];
    }

    public function getUserData(): array
    {
        return $this->accessToken ? $this->mockUser : [];
    }

    public function getInvoices(): array
    {
        // Get invoices from database for demo user
        $user = User::where('email', '<EMAIL>')->first();

        if (!$user) {
            return [];
        }

        $invoices = Invoice::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return $invoices->map(function ($invoice) {
            return [
                'id' => $invoice->bexio_id,
                'document_nr' => $invoice->document_nr,
                'title' => $invoice->contact_info['name'] ?? 'Invoice',
                'total' => $invoice->total,
                'is_valid' => $invoice->status !== 'cancelled',
                'status' => $invoice->status,
                'contact_info' => $invoice->contact_info,
                'is_recurring' => $invoice->is_recurring,
                'recurring_settings' => $invoice->recurring_settings,
                'created_at' => $invoice->created_at->format('Y-m-d'),
                'total_remaining_payments' => $invoice->status === 'paid' ? 0 : $invoice->total
            ];
        })->toArray();
    }

    public function getRecurringInvoices(): array
    {
        $user = User::where('email', '<EMAIL>')->first();

        if (!$user) {
            return [];
        }

        $recurringInvoices = Invoice::where('user_id', $user->id)
            ->where('is_recurring', true)
            ->with('recurringTemplate')
            ->orderBy('created_at', 'desc')
            ->get();

        return $recurringInvoices->map(function ($invoice) {
            return [
                'id' => $invoice->id,
                'rebill_title' => $invoice->contact_info['name'] ?? 'Recurring Invoice',
                'rebill_description' => "Invoice for {$invoice->contact_info['name']}",
                'status' => $invoice->status === 'cancelled' ? 'inactive' : 'active',
                'period' => $invoice->recurringTemplate->interval ?? 'monthly',
                'nextCharge' => $invoice->recurringTemplate ?
                    $invoice->recurringTemplate->next_run->timestamp * 1000 :
                    now()->addMonth()->timestamp * 1000,
                'total' => $invoice->total,
                'document_nr' => $invoice->document_nr,
                'DELETED' => $invoice->status === 'cancelled'
            ];
        })->toArray();
    }

    public function getDrafts(): array
    {
        $user = User::where('email', '<EMAIL>')->first();

        if (!$user) {
            return [];
        }

        $drafts = Invoice::where('user_id', $user->id)
            ->where('status', 'draft')
            ->orderBy('created_at', 'desc')
            ->get();

        return $drafts->map(function ($invoice) {
            return [
                'id' => $invoice->id,
                'created' => $invoice->created_at->timestamp * 1000,
                'draft' => [
                    'rebill_title' => $invoice->contact_info['name'] ?? '',
                    'rebill_description' => "Draft invoice for {$invoice->contact_info['name']}",
                    'customerDetails' => [
                        'name' => $invoice->contact_info['name'] ?? '',
                        'email' => $invoice->contact_info['email'] ?? '',
                        'address' => $invoice->contact_info['address'] ?? ''
                    ],
                    'total' => $invoice->total
                ]
            ];
        })->toArray();
    }
}
