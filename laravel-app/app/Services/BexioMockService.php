<?php

namespace App\Services;

class BexioMockService implements BexioServiceInterface
{
    protected ?string $accessToken = null;
    protected array $mockUser = [
        'id' => 12345,
        'email' => '<EMAIL>',
        'first_name' => 'Demo',
        'last_name' => 'User',
        'company' => 'Bexio Demo'
    ];

    public function getAuthUrl(): string
    {
        return route('bexio.auth.callback') . '?code=mock_code';
    }

    public function handleCallback(string $code): array
    {
        if ($code === 'mock_code') {
            $this->accessToken = 'mock_access_token';
            return [
                'access_token' => $this->accessToken,
                'token_type' => 'Bearer',
                'expires_in' => 3600
            ];
        }

        return [];
    }

    public function getAccessToken(): ?string
    {
        return $this->accessToken;
    }

    public function refreshToken(): array
    {
        $this->accessToken = 'refreshed_mock_token';
        return [
            'access_token' => $this->accessToken,
            'token_type' => 'Bearer',
            'expires_in' => 3600
        ];
    }

    public function getUserData(): array
    {
        return $this->accessToken ? $this->mockUser : [];
    }

    public function getInvoices(): array
    {
        return [
            [
                'id' => 1001,
                'document_nr' => 'INV-2023-1001',
                'title' => 'Website Development',
                'total' => 1500.00,
                'is_valid' => true
            ],
            [
                'id' => 1002,
                'document_nr' => 'INV-2023-1002',
                'title' => 'Monthly Maintenance',
                'total' => 300.00,
                'is_valid' => true
            ]
        ];
    }
}
