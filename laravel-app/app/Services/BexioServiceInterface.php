<?php

namespace App\Services;

interface BexioServiceInterface
{
    public function getAuthUrl(): string;
    public function handleCallback(string $code): array;
    public function getAccessToken(): ?string;
    public function setAccessToken(string $token): void;
    public function refreshToken(): array;
    public function getUserData(): array;
    public function getInvoices(): array;
    public function getRecurringInvoices(): array;
    public function getDrafts(): array;
}
