<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class BexioRealService implements BexioServiceInterface
{
    protected string $clientId;
    protected string $clientSecret;
    protected string $redirectUri;
    protected ?string $accessToken = null;

    public function __construct()
    {
        $this->clientId = config('services.bexio.client_id');
        $this->clientSecret = config('services.bexio.client_secret');
        $this->redirectUri = config('services.bexio.redirect');
    }

    public function getAuthUrl(): string
    {
        return 'https://office.bexio.com/oauth/authorize?' . http_build_query([
            'client_id' => $this->clientId,
            'redirect_uri' => $this->redirectUri,
            'response_type' => 'code',
            'scope' => 'openid profile email'
        ]);
    }

    public function handleCallback(string $code): array
    {
        $response = Http::asForm()->post('https://office.bexio.com/oauth/token', [
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret,
            'code' => $code,
            'grant_type' => 'authorization_code',
            'redirect_uri' => $this->redirectUri
        ]);

        $data = $response->json();
        $this->accessToken = $data['access_token'] ?? null;

        return $data;
    }

    public function getAccessToken(): ?string
    {
        return $this->accessToken;
    }

    public function setAccessToken(string $token): void
    {
        $this->accessToken = $token;
    }

    public function refreshToken(): array
    {
        // Implementation for token refresh
        return [];
    }

    public function getUserData(): array
    {
        if (!$this->accessToken) {
            return [];
        }

        $response = Http::withToken($this->accessToken)
            ->get('https://office.bexio.com/api/v1/users/me');

        return $response->json();
    }

    public function getCompanyProfile(): array
    {
        if (!$this->accessToken) {
            return [];
        }

        $response = Http::withToken($this->accessToken)
            ->get('https://api.bexio.com/2.0/company_profile');

        if ($response->successful()) {
            return $response->json();
        }

        return [];
    }

    public function getInvoices(): array
    {
        if (!$this->accessToken) {
            return [];
        }

        $response = Http::withToken($this->accessToken)
            ->get('https://api.bexio.com/2.0/kb_invoice', [
                'limit' => 100
            ]);

        if ($response->successful()) {
            return $response->json();
        }

        return [];
    }

    public function getRecurringInvoices(): array
    {
        // Implementation for real Bexio API
        // For now, return empty array
        return [];
    }

    public function getDrafts(): array
    {
        // Implementation for real Bexio API
        // For now, return empty array
        return [];
    }
}
