<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Bexio Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Bexio OAuth integration
    |
    */

    'mode' => env('BEXIO_MODE', 'demo'), // 'demo' or 'real'
    
    'client_id' => env('BEXIO_CLIENT_ID', 'fb7bcebd-1402-4791-b27d-e5f742f01490'),
    
    'client_secret' => env('BEXIO_CLIENT_SECRET', 'AO8Wr1q37jA82R8DMpcpejj_8x49t81RBW5lWGGKawvIBxkZ7eBC7Ixx4LyiS92D7VGdDiA57ASqowoF2AcKnJ4'),
    
    'redirect_uri' => env('BEXIO_REDIRECT_URI', 'http://127.0.0.1:8001/auth/bexio/callback'),
    
    'scopes' => [
        'openid',
        'profile',
        'email',
        'offline_access',
        'kb_invoice_edit',
        'contact_edit',
        'company_profile',
    ],
    
    'auth_url' => 'https://auth.bexio.com/oauth/authorize',
    'token_url' => 'https://auth.bexio.com/oauth/access_token',
    'api_base_url' => 'https://api.bexio.com',
];
